import {
  <PERSON>,
  Get,
  Post,
  Body,
  <PERSON>,
  Param,
  Res,
  HttpStatus,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { Response } from 'express';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  async create(
    @Body() createUserDto: CreateUserDto,
    @Res() res: Response,
  ): Promise<void> {
    try {
      await this.usersService.create(createUserDto);
      return res.redirect('back');
    } catch (error) {
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: 'Error creating user' });
    }
  }

  @Get()
  async findAll(@Res() res: Response) {
    const users = await this.usersService.findAll();
    // Render the view template with a custom layout
    return res.render('admin/users/users', {
      layout: 'layouts/adminLayout',
      title: 'Nomad - Users',
      users: users,
    });
  }

  @Get('api/users')
  async findAllApi(): Promise<any[]> {
    return this.usersService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.usersService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.usersService.update(+id, updateUserDto);
  }

  @Get(':id/delete')
  async remove(@Res() res: Response, @Param('id') id: string) {
    await this.usersService.remove(id);
    return res.redirect('back');
  }
}
