import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { Company } from './entities/company.entity';
import { User } from '../users/entities/user.entity';

@Injectable()
export class CompaniesService {
  constructor(
    @InjectRepository(Company)
    private readonly companyRepository: Repository<Company>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async create(createCompanyDto: CreateCompanyDto): Promise<Company> {
    const {
      registeredName,
      logo,
      pocName,
      pocEmail,
      pocNumber,
      pocDesignation,
    } = createCompanyDto;
    const company = this.companyRepository.create({
      registeredName,
      logo,
      pocName,
      pocEmail,
      pocNumber,
      pocDesignation,
    });
    console.log('company', company);
    return this.companyRepository.save(company);
  }

  async findAll(): Promise<Company[]> {
    return this.companyRepository.find();
  }

  async findOne(ID: string): Promise<Company> {
    const company = await this.companyRepository.findOne({ where: { ID } });
    if (!company) {
      throw new NotFoundException(`Company with ID ${ID} not found`);
    }
    return company;
  }

  async findAllMembers(ID: string): Promise<User[]> {
    const users = await this.userRepository.find({
      where: { companyID: ID },
    });
    if (!users) {
      throw new NotFoundException(`Users with ID ${ID} not found`);
    }
    return users;
  }

  async update(
    ID: string,
    updateCompanyDto: UpdateCompanyDto,
  ): Promise<Company> {
    const company = await this.findOne(ID);
    const {
      registeredName,
      logo,
      pocName,
      pocEmail,
      pocNumber,
      pocDesignation,
    } = updateCompanyDto;
    if (registeredName) company.registeredName = registeredName;
    if (logo) company.logo = logo;
    if (pocName) company.pocName = pocName;
    if (pocEmail) company.pocEmail = pocEmail;
    if (pocNumber) company.pocNumber = pocNumber;
    if (pocDesignation) company.pocDesignation = pocDesignation;

    return this.companyRepository.save(company);
  }

  async remove(ID: string): Promise<void> {
    const company = await this.findOne(ID);
    await this.companyRepository.remove(company);
  }
}
