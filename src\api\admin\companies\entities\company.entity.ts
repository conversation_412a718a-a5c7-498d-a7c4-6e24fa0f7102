// company.entity.ts
import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Survey } from '../../surveys/entities/survey.entity';
import { IsEmail, IsOptional } from 'class-validator';

@Entity()
export class Company {
  @PrimaryGeneratedColumn('uuid')
  ID?: string;

  @Column()
  registeredName: string;

  @Column({ nullable: true })
  @IsOptional()
  logo?: string;

  @Column()
  pocName: string;

  @Column()
  @IsEmail({}, { message: 'Invalid email format' })
  pocEmail: string;

  @Column()
  pocNumber: string;

  @Column()
  pocDesignation: string;

  @CreateDateColumn({ name: 'created_at' }) // Automatically set the created_at timestamp
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' }) // Automatically set the updated_at timestamp
  updatedAt: Date;

  @OneToMany(() => User, (user) => user.company)
  users: User[];

  @OneToMany(() => Survey, (survey) => survey.company)
  surveys: Survey[];
}
