import { Role } from 'src/api/admin/roles/entities/role.entity';
import { User } from 'src/api/admin/users/entities/user.entity';
import * as dotenv from 'dotenv';
import { Company } from 'src/api/admin/companies/entities/company.entity';
import { Survey } from 'src/api/admin/surveys/entities/survey.entity';
import { SurveySubject } from 'src/api/admin/survey-subjects/entities/survey-subject.entity';
import { SurveyEvaluation } from 'src/api/admin/survey-evaluations/entities/survey-evaluation.entity';
import { SurveyEvaluator } from 'src/api/admin/survey-evaluators/entities/survey-evaluator.entity';
dotenv.config();
import migrations from '../migrations';

const isExternalDB = process.env.DB_HOST && !process.env.DB_HOST.includes('localhost');

export const databaseConfig = {
  type: process.env.DB_TYPE,
  database: process.env.DB_NAME,
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT, 10),
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  ...(isExternalDB && {
    ssl: {
      rejectUnauthorized: false,
    },
  }),
};

// mapping function to convert the database configuration for TypeORM
export const toTypeOrmConfig = (config: any) => ({
  ...config,
  entities: [
    User,
    Role,
    Company,
    Survey,
    SurveySubject,
    SurveyEvaluation,
    SurveyEvaluator,
  ],
  synchronize: true, // Enable for initial setup, disable in production
  logging: true, // Enable query logging
  migrationsRun: false, // Disable migrations temporarily
  // dropSchema: false,
  migrationsTransactionMode: 'each',
  migrations,
  subscribers: [],
});

// mapping function to convert the database configuration for pg.Pool
export const toPgPoolConfig = (config: any) => ({
  ...config,
  user: config.username, // Map 'username' to 'user'
});
