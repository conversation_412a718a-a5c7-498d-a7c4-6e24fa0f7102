// src/modules/company/company.controller.ts
import {
  Controller,
  Get,
  Post,
  Param,
  Body,
  Put,
  Delete,
  Res,
  HttpStatus,
} from '@nestjs/common';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { CompaniesService } from './companies.service';
import { Company } from './entities/company.entity';
import { Response } from 'express';

@Controller('companies')
export class CompaniesController {
  constructor(private readonly companiesService: CompaniesService) {}

  @Post()
  async create(
    @Res() res: Response,
    @Body() createCompanyDto: CreateCompanyDto,
  ): Promise<void> {
    try {
      await this.companiesService.create(createCompanyDto);
      return res.redirect('back');
    } catch (error) {
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: 'Error creating user' });
    }
  }

  @Get()
  async findAll(@Res() res: Response) {
    const companies = await this.companiesService.findAll();
    // Render the view template with a custom layout
    return res.render('admin/companies/companies', {
      layout: 'layouts/adminLayout',
      title: 'Nomad - Companies',
      companies: companies,
    });
  }

  @Get(':ID/members')
  async findAllMembers(@Param('ID') ID: string, @Res() res: Response) {
    const members = await this.companiesService.findAllMembers(ID);
    console.log('members', members);
    // Render the view template with a custom layout
    return res.render('admin/companies/companyMembers', {
      layout: 'layouts/adminLayout',
      title: 'Nomad - Company Members',
      members: members,
      companyID: ID,
    });
  }

  @Get(':ID')
  async findOne(@Param('ID') ID: string): Promise<Company> {
    return this.companiesService.findOne(ID);
  }

  @Put(':ID')
  async update(
    @Param('ID') ID: string,
    @Body() updateCompanyDto: UpdateCompanyDto,
  ): Promise<Company> {
    return this.companiesService.update(ID, updateCompanyDto);
  }

  @Delete(':id')
  async remove(@Param('id') ID: string): Promise<void> {
    return this.companiesService.remove(ID);
  }
}
