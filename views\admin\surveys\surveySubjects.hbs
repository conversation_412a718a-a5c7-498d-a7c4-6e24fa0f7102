<!-- prettier-ignore -->

<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
    <!--begin::Content wrapper-->
    <div class="d-flex flex-column flex-column-fluid">

        <!--begin::Content-->
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <!--begin::Content container-->
            <div id="kt_app_content_container" class="app-container container-xxl">
                <!--begin::Navbar-->
                {{> surveyDetailsNavbar}}
                <!--end::Navbar-->
                <!--begin::Row-->
                <!--begin::Card-->
                <div class="card">
                    <!--begin::Card header-->
                    <div class="card-header border-0 pt-6">
                        <!--begin::Card title-->
                        <div class="card-title">
                            <!--begin::Search-->
                            <div class="d-flex align-items-center position-relative my-1">
                                <i class="ki-duotone ki-magnifier fs-3 position-absolute ms-5">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                                <input type="text" data-kt-user-table-filter="search"
                                    class="form-control form-control-solid w-250px ps-13" placeholder="Search user">
                            </div>
                            <!--end::Search-->
                        </div>
                        <!--begin::Card title-->
                        <!--begin::Card toolbar-->
                        <div class="card-toolbar">
                            <!--begin::Toolbar-->
                            <div class="d-flex justify-content-end" data-kt-user-table-toolbar="base">
                                <!--begin::Add user-->
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                                    data-bs-target="#kt_modal_add_subject">
                                    <i class="ki-duotone ki-plus fs-2"></i>Add Subject</button>
                                <!--end::Add user-->
                            </div>
                            <!--end::Toolbar-->
                        </div>
                        <!--end::Card toolbar-->
                    </div>
                    <!--end::Card header-->
                    <!--begin::Card body-->
                    <div class="card-body py-4">
                        <!--begin::Table-->
                        <div id="kt_table_users_wrapper" class="dt-container dt-bootstrap5 dt-empty-footer">
                            <div id="" class="table-responsive">
                                <table class="table align-middle table-row-dashed fs-6 gy-5 dataTable"
                                    id="kt_table_users" style="width: 1199.5px;">
                                    <colgroup>
                                        <col style="width: 36.3984px;">
                                        <col style="width: 193.875px;">
                                        <col style="width: 186.195px;">
                                        <col style="width: 186.195px;">
                                        <col style="width: 186.195px;">
                                        <col style="width: 253.984px;">
                                        <col style="width: 156.656px;">
                                    </colgroup>
                                    <thead>
                                        <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0" role="row">
                                            <th class="min-w-125px dt-orderable-asc dt-orderable-desc"
                                                data-dt-column="1" rowspan="1" colspan="1"
                                                aria-label="User: Activate to sort" tabindex="0"><span
                                                    class="dt-column-title" role="button">Subject</span><span
                                                    class="dt-column-order"></span></th>

                                            <th class="min-w-125px dt-orderable-asc dt-orderable-desc"
                                                data-dt-column="3" rowspan="1" colspan="1"
                                                aria-label="Last login: Activate to sort" tabindex="0"><span
                                                    class="dt-column-title" role="button">Last login</span><span
                                                    class="dt-column-order"></span></th>
                                            <th class="min-w-125px dt-orderable-asc dt-orderable-desc"
                                                data-dt-column="4" rowspan="1" colspan="1"
                                                aria-label="Two-step: Activate to sort" tabindex="0"><span
                                                    class="dt-column-title" role="button">Role</span><span
                                                    class="dt-column-order"></span></th>
                                            <th class="min-w-125px dt-orderable-asc dt-orderable-desc"
                                                data-dt-column="5" rowspan="1" colspan="1"
                                                aria-label="Joined Date: Activate to sort" tabindex="0"><span
                                                    class="dt-column-title" role="button">Joined Date</span><span
                                                    class="dt-column-order"></span></th>
                                            <th class="text-end min-w-100px dt-orderable-none" data-dt-column="6"
                                                rowspan="1" colspan="1" aria-label="Actions"><span
                                                    class="dt-column-title">Actions</span><span
                                                    class="dt-column-order"></span></th>
                                        </tr>
                                    </thead>
                                    <tbody class="text-gray-600 fw-semibold">
                                        {{#each survey.subjects}}
                                        <tr>
                                            <td class="d-flex align-items-center">
                                                <!--begin:: Avatar -->
                                                <div class="symbol symbol-circle symbol-50px overflow-hidden me-3">
                                                    <a href="apps/user-management/users/view.html">
                                                        <div class="symbol-label fs-3 bg-light-danger text-danger">
                                                            {{initials user.firstName user.lastName}}
                                                        </div>
                                                    </a>
                                                </div>
                                                <!--end::Avatar-->
                                                <!--begin::User details-->
                                                <div class="d-flex flex-column">
                                                    <a href="apps/user-management/users/view.html"
                                                        class="text-gray-800 text-hover-primary mb-1">{{user.firstName}}
                                                        {{user.lastName}}</a>
                                                    <span>{{user.email}}</span>
                                                </div>
                                                <!--begin::User details-->
                                            </td>
                                            <td data-order="2024-05-30T13:58:10+05:00">
                                                <div class="badge badge-light fw-bold">20 mins ago</div>
                                            </td>
                                            <td>
                                                <div class="badge badge-light-success fw-bold">Member</div>
                                            </td>
                                            <td data-order="2024-09-22T22:10:00+05:00">22 Sep 2024, 10:10 pm</td>
                                            <td class="text-end">
                                                <a href="#"
                                                    class="btn btn-light btn-active-light-primary btn-flex btn-center btn-sm"
                                                    data-kt-menu-trigger="click"
                                                    data-kt-menu-placement="bottom-end">Actions
                                                    <i class="ki-duotone ki-down fs-5 ms-1"></i></a>
                                                <!--begin::Menu-->
                                                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4"
                                                    data-kt-menu="true">
                                                    <!--begin::Menu item-->
                                                    <div class="menu-item px-3">
                                                        <a href="#" class="menu-link px-3" data-bs-toggle="modal"
                                                            data-bs-target="#kt_modal_add_evaluator">Add
                                                            Evaluator</a>
                                                    </div>
                                                    <!--end::Menu item-->
                                                    <!--begin::Menu item-->
                                                    <div class="menu-item px-3">
                                                        <a href="apps/user-management/users/view.html"
                                                            class="menu-link px-3">Edit</a>
                                                    </div>
                                                    <!--end::Menu item-->
                                                    <!--begin::Menu item-->
                                                    <div class="menu-item px-3">
                                                        <a href="#" class="menu-link px-3"
                                                            data-kt-users-table-filter="delete_row">Delete</a>
                                                    </div>
                                                    <!--end::Menu item-->
                                                </div>
                                                <!--end::Menu-->
                                            </td>
                                        </tr>
                                        {{/each}}
                                    </tbody>
                                    <tfoot></tfoot>
                                </table>
                            </div>
                            <div id="" class="row">
                                <div id=""
                                    class="col-sm-12 col-md-5 d-flex align-items-center justify-content-center justify-content-md-start dt-toolbar">
                                </div>
                                <div id=""
                                    class="col-sm-12 col-md-7 d-flex align-items-center justify-content-center justify-content-md-end">
                                    <div class="dt-paging paging_simple_numbers">
                                        <ul class="pagination">
                                            <li class="dt-paging-button page-item disabled"><a
                                                    class="page-link previous" aria-controls="kt_table_users"
                                                    aria-disabled="true" aria-label="Previous" data-dt-idx="previous"
                                                    tabindex="-1"><i class="previous"></i></a></li>
                                            <li class="dt-paging-button page-item active"><a href="#" class="page-link"
                                                    aria-controls="kt_table_users" aria-current="page" data-dt-idx="0"
                                                    tabindex="0">1</a></li>
                                            <li class="dt-paging-button page-item"><a href="#" class="page-link"
                                                    aria-controls="kt_table_users" data-dt-idx="1" tabindex="0">2</a>
                                            </li>
                                            <li class="dt-paging-button page-item"><a href="#" class="page-link"
                                                    aria-controls="kt_table_users" data-dt-idx="2" tabindex="0">3</a>
                                            </li>
                                            <li class="dt-paging-button page-item"><a href="#" class="page-link next"
                                                    aria-controls="kt_table_users" aria-label="Next" data-dt-idx="next"
                                                    tabindex="0"><i class="next"></i></a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--end::Table-->
                    </div>
                    <!--end::Card body-->
                </div>
                <!--end::Card-->
                <!--end::Row-->
            </div>
            <!--end::Content container-->
        </div>
        <!--end::Content-->
    </div>
    <!--end::Content wrapper-->

    <!--begin::Footer-->
    {{> surveyDetailsFooter}}
    <!--end::Footer-->
</div>

{{> surveyDetailsModals}}

{{#block 'scripts'}}

{{/block}}