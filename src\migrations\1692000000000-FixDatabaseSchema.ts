import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixDatabaseSchema1692000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, let's check if the tables exist and handle the schema properly
    
    // 1. Handle company table - make registeredName nullable and set default values
    const companyTableExists = await queryRunner.hasTable('company');
    if (companyTableExists) {
      // Check if registeredName column exists
      const hasRegisteredName = await queryRunner.hasColumn('company', 'registeredName');
      if (!hasRegisteredName) {
        // Add the column as nullable first
        await queryRunner.query(`
          ALTER TABLE "company" ADD COLUMN "registeredName" character varying;
        `);
      } else {
        // Make existing column nullable
        await queryRunner.query(`
          ALTER TABLE "company" ALTER COLUMN "registeredName" DROP NOT NULL;
        `);
      }
      
      // Update null values with a default value
      await queryRunner.query(`
        UPDATE "company" SET "registeredName" = 'Default Company Name' WHERE "registeredName" IS NULL;
      `);
    }

    // 2. Handle role table - add timestamps if they don't exist
    const roleTableExists = await queryRunner.hasTable('role');
    if (roleTableExists) {
      const hasCreatedAt = await queryRunner.hasColumn('role', 'created_at');
      const hasUpdatedAt = await queryRunner.hasColumn('role', 'updated_at');
      
      if (!hasCreatedAt) {
        await queryRunner.query(`
          ALTER TABLE "role" ADD COLUMN "created_at" TIMESTAMP NOT NULL DEFAULT now();
        `);
      }
      
      if (!hasUpdatedAt) {
        await queryRunner.query(`
          ALTER TABLE "role" ADD COLUMN "updated_at" TIMESTAMP NOT NULL DEFAULT now();
        `);
      }
    }

    // 3. Create survey-related tables if they don't exist
    const surveyTableExists = await queryRunner.hasTable('survey');
    if (!surveyTableExists) {
      await queryRunner.query(`
        CREATE TABLE "survey" (
          "ID" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "title" character varying NOT NULL,
          "description" character varying NOT NULL,
          "created_at" TIMESTAMP NOT NULL DEFAULT now(),
          "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
          "companyID" uuid,
          CONSTRAINT "PK_3afba146a5330961cfddd4fd249" PRIMARY KEY ("ID")
        );
      `);
    }

    const surveyEvaluatorExists = await queryRunner.hasTable('survey_evaluator');
    if (!surveyEvaluatorExists) {
      await queryRunner.query(`
        CREATE TABLE "survey_evaluator" (
          "ID" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "uniqueIdentifier" integer NOT NULL,
          "created_at" TIMESTAMP NOT NULL DEFAULT now(),
          "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
          "userID" uuid NOT NULL,
          "surveyID" uuid NOT NULL,
          CONSTRAINT "UQ_2def54104aa719f782c35c18203" UNIQUE ("userID", "surveyID"),
          CONSTRAINT "PK_39fd8a6c1bbf6d92447b15bbd66" PRIMARY KEY ("ID")
        );
      `);
    }

    const surveySubjectExists = await queryRunner.hasTable('survey_subject');
    if (!surveySubjectExists) {
      await queryRunner.query(`
        CREATE TABLE "survey_subject" (
          "ID" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "uniqueIdentifier" integer NOT NULL,
          "created_at" TIMESTAMP NOT NULL DEFAULT now(),
          "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
          "userID" uuid NOT NULL,
          "surveyID" uuid NOT NULL,
          CONSTRAINT "UQ_a6a31182a384da6f69ffbe7929f" UNIQUE ("userID", "surveyID"),
          CONSTRAINT "PK_3b2d886bd6e5208c36c56772664" PRIMARY KEY ("ID")
        );
      `);
    }

    const surveyEvaluationExists = await queryRunner.hasTable('survey_evaluation');
    if (!surveyEvaluationExists) {
      await queryRunner.query(`
        CREATE TABLE "survey_evaluation" (
          "ID" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "relation" character varying NOT NULL,
          "marks" integer,
          "feedback" character varying,
          "created_at" TIMESTAMP NOT NULL DEFAULT now(),
          "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
          "surveyID" uuid NOT NULL,
          "surveySubjectID" uuid NOT NULL,
          "surveyEvaluatorID" uuid,
          CONSTRAINT "UQ_2c0defd870ee38ddaa5a8513104" UNIQUE ("surveySubjectID", "surveyEvaluatorID", "surveyID"),
          CONSTRAINT "PK_0f7e8c35103f6dda2050f59b574" PRIMARY KEY ("ID")
        );
      `);
    }

    // 4. Add foreign key constraints if they don't exist
    try {
      await queryRunner.query(`
        ALTER TABLE "survey" ADD CONSTRAINT "FK_survey_company" 
        FOREIGN KEY ("companyID") REFERENCES "company"("ID") ON DELETE CASCADE;
      `);
    } catch (error) {
      // Constraint might already exist, ignore error
    }

    try {
      await queryRunner.query(`
        ALTER TABLE "survey_evaluator" ADD CONSTRAINT "FK_survey_evaluator_user" 
        FOREIGN KEY ("userID") REFERENCES "user"("ID") ON DELETE CASCADE;
      `);
    } catch (error) {
      // Constraint might already exist, ignore error
    }

    try {
      await queryRunner.query(`
        ALTER TABLE "survey_evaluator" ADD CONSTRAINT "FK_survey_evaluator_survey" 
        FOREIGN KEY ("surveyID") REFERENCES "survey"("ID") ON DELETE CASCADE;
      `);
    } catch (error) {
      // Constraint might already exist, ignore error
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Reverse the changes if needed
    await queryRunner.query(`DROP TABLE IF EXISTS "survey_evaluation" CASCADE;`);
    await queryRunner.query(`DROP TABLE IF EXISTS "survey_subject" CASCADE;`);
    await queryRunner.query(`DROP TABLE IF EXISTS "survey_evaluator" CASCADE;`);
    await queryRunner.query(`DROP TABLE IF EXISTS "survey" CASCADE;`);
    
    // Remove added columns
    const companyTableExists = await queryRunner.hasTable('company');
    if (companyTableExists) {
      const hasRegisteredName = await queryRunner.hasColumn('company', 'registeredName');
      if (hasRegisteredName) {
        await queryRunner.query(`ALTER TABLE "company" DROP COLUMN "registeredName";`);
      }
    }
  }
}
