# API Modifications for Network Tab Visibility

## 1. Create JSON API Endpoints

Add these new endpoints to your controllers:

### Users Controller
```typescript
@Get('api/users')
async findAllJson(): Promise<User[]> {
  return this.usersService.findAll();
}
```

### Companies Controller
```typescript
@Get('api/companies')
async findAllJson(): Promise<Company[]> {
  return this.companiesService.findAll();
}
```

### Surveys Controller
```typescript
@Get('api/surveys')
async findAllJson(): Promise<Survey[]> {
  return this.surveysService.findAll();
}
```

## 2. Modify HBS Templates

Replace server-side data rendering with client-side loading:

### Before (Server-side):
```handlebars
{{#each users}}
<tr>
  <td>{{firstName}} {{lastName}}</td>
  <td>{{email}}</td>
</tr>
{{/each}}
```

### After (Client-side):
```handlebars
<tbody id="users-table-body">
  <!-- Data will be loaded via JavaScript -->
</tbody>

<script>
// This will appear in Network tab
fetch('/api/users')
  .then(response => response.json())
  .then(users => {
    const tbody = document.getElementById('users-table-body');
    tbody.innerHTML = users.map(user => `
      <tr>
        <td>${user.firstName} ${user.lastName}</td>
        <td>${user.email}</td>
      </tr>
    `).join('');
  });
</script>
```

## 3. Form Submissions via AJAX

Convert form submissions to AJAX calls:

### Before (Traditional form):
```html
<form action="/users" method="post">
  <!-- form fields -->
  <button type="submit">Submit</button>
</form>
```

### After (AJAX form):
```html
<form id="user-form">
  <!-- form fields -->
  <button type="button" onclick="submitForm()">Submit</button>
</form>

<script>
function submitForm() {
  const formData = new FormData(document.getElementById('user-form'));
  
  // This will appear in Network tab
  fetch('/api/users', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    // Handle success
    location.reload(); // or update UI dynamically
  });
}
</script>
```

## 4. Quick Win: Add Parallel API Endpoints

Add these to existing controllers without changing current functionality:

### Users Controller Addition
```typescript
@Get('api/users')
async getUsersApi(): Promise<User[]> {
  return this.usersService.findAll();
}

@Post('api/users')
async createUserApi(@Body() createUserDto: CreateUserDto): Promise<User> {
  return this.usersService.create(createUserDto);
}
```

### Companies Controller Addition
```typescript
@Get('api/companies')
async getCompaniesApi(): Promise<Company[]> {
  return this.companiesService.findAll();
}

@Get('api/companies/:id/members')
async getCompanyMembersApi(@Param('id') id: string): Promise<any[]> {
  return this.companiesService.findAllMembers(id);
}
```
