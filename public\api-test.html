<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - Network Tab Demo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { margin: 10px; padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin: 20px 0; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; }
        pre { background: #e9ecef; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>API Network Tab Demo</h1>
    <p>Open your browser's Developer Tools (F12) and go to the Network tab. Then click the buttons below to see API calls.</p>
    
    <div>
        <button onclick="fetchUsers()">Fetch Users (API Call)</button>
        <button onclick="fetchCompanies()">Fetch Companies (API Call)</button>
        <button onclick="fetchSurveys()">Fetch Surveys (API Call)</button>
        <button onclick="createTestUser()">Create Test User (POST API Call)</button>
    </div>
    
    <div id="result" class="result">
        <h3>Results will appear here:</h3>
        <pre id="output">Click a button above to make an API call...</pre>
    </div>

    <script>
        function displayResult(title, data) {
            const output = document.getElementById('output');
            output.textContent = `${title}\n\n${JSON.stringify(data, null, 2)}`;
        }

        function displayError(title, error) {
            const output = document.getElementById('output');
            output.textContent = `${title}\n\nError: ${error.message}`;
        }

        async function fetchUsers() {
            try {
                console.log('Making API call to /users/api/users');
                const response = await fetch('/users/api/users');
                const data = await response.json();
                displayResult('Users API Response:', data);
            } catch (error) {
                displayError('Users API Error:', error);
            }
        }

        async function fetchCompanies() {
            try {
                console.log('Making API call to /companies');
                const response = await fetch('/companies');
                const data = await response.json();
                displayResult('Companies API Response:', data);
            } catch (error) {
                displayError('Companies API Error:', error);
            }
        }

        async function fetchSurveys() {
            try {
                console.log('Making API call to /surveys');
                const response = await fetch('/surveys');
                const data = await response.json();
                displayResult('Surveys API Response:', data);
            } catch (error) {
                displayError('Surveys API Error:', error);
            }
        }

        async function createTestUser() {
            try {
                console.log('Making POST API call to /users');
                const response = await fetch('/users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        firstName: 'Test',
                        lastName: 'User',
                        email: '<EMAIL>',
                        password: 'password123',
                        phone: '+1234567890',
                        role: 'admin'
                    })
                });
                
                if (response.ok) {
                    displayResult('User Created Successfully:', { status: 'success', redirected: response.redirected });
                } else {
                    const errorData = await response.text();
                    displayResult('User Creation Response:', errorData);
                }
            } catch (error) {
                displayError('User Creation Error:', error);
            }
        }
    </script>
</body>
</html>
