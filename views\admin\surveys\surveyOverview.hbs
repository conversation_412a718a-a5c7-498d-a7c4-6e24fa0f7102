<!-- prettier-ignore -->

<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
    <!--begin::Content wrapper-->
    <div class="d-flex flex-column flex-column-fluid">

        <!--begin::Content-->
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <!--begin::Content container-->
            <div id="kt_app_content_container" class="app-container container-xxl">
                <!--begin::Navbar-->
                {{> surveyDetailsNavbar}}
                <!--end::Navbar-->
                <!--begin::Row-->
                <div class="row gx-6 gx-xl-9">
                    <!--begin::Col-->
                    <div class="col-lg-6">
                        <!--begin::Summary-->
                        <div class="card card-flush h-lg-100">
                            <!--begin::Card header-->
                            <div class="card-header mt-6">
                                <!--begin::Card title-->
                                <div class="card-title flex-column">
                                    <h3 class="fw-bold mb-1">Evaluations Summary</h3>
                                    <div class="fs-6 fw-semibold text-gray-500">24 Overdue Evaluations</div>
                                </div>
                                <!--end::Card title-->
                                <!--begin::Card toolbar-->
                                <div class="card-toolbar">
                                    <a href="#" class="btn btn-light btn-sm">View Evaluations</a>
                                </div>
                                <!--end::Card toolbar-->
                            </div>
                            <!--end::Card header-->
                            <!--begin::Card body-->
                            <div class="card-body p-9 pt-5">
                                <!--begin::Wrapper-->
                                <div class="d-flex flex-wrap">
                                    <!--begin::Chart-->
                                    <div class="position-relative d-flex flex-center h-175px w-175px me-15 mb-7">
                                        <div
                                            class="position-absolute translate-middle start-50 top-50 d-flex flex-column flex-center">
                                            <span class="fs-2qx fw-bold">237</span>
                                            <span class="fs-6 fw-semibold text-gray-500">Total Evaluations</span>
                                        </div>
                                        <canvas id="project_overview_chart" width="175" height="175"
                                            style="display: block; box-sizing: border-box; height: 175px; width: 175px;"></canvas>
                                    </div>
                                    <!--end::Chart-->
                                    <!--begin::Labels-->
                                    <div class="d-flex flex-column justify-content-center flex-row-fluid pe-11 mb-5">
                                        <!--begin::Label-->
                                        <div class="d-flex fs-6 fw-semibold align-items-center mb-3">
                                            <div class="bullet bg-primary me-3"></div>
                                            <div class="text-gray-500">Active</div>
                                            <div class="ms-auto fw-bold text-gray-700">30</div>
                                        </div>
                                        <!--end::Label-->
                                        <!--begin::Label-->
                                        <div class="d-flex fs-6 fw-semibold align-items-center mb-3">
                                            <div class="bullet bg-success me-3"></div>
                                            <div class="text-gray-500">Completed</div>
                                            <div class="ms-auto fw-bold text-gray-700">45</div>
                                        </div>
                                        <!--end::Label-->
                                        <!--begin::Label-->
                                        <div class="d-flex fs-6 fw-semibold align-items-center mb-3">
                                            <div class="bullet bg-danger me-3"></div>
                                            <div class="text-gray-500">Overdue</div>
                                            <div class="ms-auto fw-bold text-gray-700">0</div>
                                        </div>
                                        <!--end::Label-->
                                        <!--begin::Label-->
                                        <div class="d-flex fs-6 fw-semibold align-items-center">
                                            <div class="bullet bg-gray-300 me-3"></div>
                                            <div class="text-gray-500">Yet to start</div>
                                            <div class="ms-auto fw-bold text-gray-700">25</div>
                                        </div>
                                        <!--end::Label-->
                                    </div>
                                    <!--end::Labels-->
                                </div>
                                <!--end::Wrapper-->
                                <!--begin::Notice-->
                                <div
                                    class="notice d-flex bg-light-primary rounded border-primary border border-dashed p-6">
                                    <!--begin::Wrapper-->
                                    <div class="d-flex flex-stack flex-grow-1">
                                        <!--begin::Content-->
                                        <div class="fw-semibold">
                                            <div class="fs-6 text-gray-700">
                                                <a href="#" class="fw-bold me-1">Invite New .NET Collaborators</a>to
                                                create great outstanding business to business .jsp modutr class scripts
                                            </div>
                                        </div>
                                        <!--end::Content-->
                                    </div>
                                    <!--end::Wrapper-->
                                </div>
                                <!--end::Notice-->
                            </div>
                            <!--end::Card body-->
                        </div>
                        <!--end::Summary-->
                    </div>
                    <!--end::Col-->
                    <!--begin::Col-->
                    <div class="col-lg-6">
                        <!--begin::Graph-->
                        <div class="card card-flush h-lg-100">
                            <!--begin::Card header-->
                            <div class="card-header mt-6">
                                <!--begin::Card title-->
                                <div class="card-title flex-column">
                                    <h3 class="fw-bold mb-1">Evaluations Over Time</h3>
                                    <!--begin::Labels-->
                                    <div class="fs-6 d-flex text-gray-500 fs-6 fw-semibold">
                                        <!--begin::Label-->
                                        <div class="d-flex align-items-center me-6">
                                            <span class="menu-bullet d-flex align-items-center me-2">
                                                <span class="bullet bg-success"></span>
                                            </span>Complete
                                        </div>
                                        <!--end::Label-->
                                        <!--begin::Label-->
                                        <div class="d-flex align-items-center">
                                            <span class="menu-bullet d-flex align-items-center me-2">
                                                <span class="bullet bg-primary"></span>
                                            </span>Incomplete
                                        </div>
                                        <!--end::Label-->
                                    </div>
                                    <!--end::Labels-->
                                </div>
                                <!--end::Card title-->
                                <!--begin::Card toolbar-->
                                <div class="card-toolbar">
                                    <!--begin::Select-->
                                    <select name="status" data-control="select2" data-hide-search="true"
                                        class="form-select form-select-solid form-select-sm fw-bold w-100px select2-hidden-accessible"
                                        data-select2-id="select2-data-9-zayo" tabindex="-1" aria-hidden="true"
                                        data-kt-initialized="1">
                                        <option value="1">2020 Q1</option>
                                        <option value="2">2020 Q2</option>
                                        <option value="3" selected="selected" data-select2-id="select2-data-11-c21t">
                                            2020 Q3</option>
                                        <option value="4">2020 Q4</option>
                                    </select><span class="select2 select2-container select2-container--bootstrap5"
                                        dir="ltr" data-select2-id="select2-data-10-u23g" style="width: 100%;"><span
                                            class="selection"><span
                                                class="select2-selection select2-selection--single form-select form-select-solid form-select-sm fw-bold w-100px"
                                                role="combobox" aria-haspopup="true" aria-expanded="false" tabindex="0"
                                                aria-disabled="false" aria-labelledby="select2-status-ll-container"
                                                aria-controls="select2-status-ll-container"><span
                                                    class="select2-selection__rendered" id="select2-status-ll-container"
                                                    role="textbox" aria-readonly="true" title="2020 Q3">2020
                                                    Q3</span><span class="select2-selection__arrow"
                                                    role="presentation"><b
                                                        role="presentation"></b></span></span></span><span
                                            class="dropdown-wrapper" aria-hidden="true"></span></span>
                                    <!--end::Select-->
                                </div>
                                <!--end::Card toolbar-->
                            </div>
                            <!--end::Card header-->
                            <!--begin::Card body-->
                            <div class="card-body pt-10 pb-0 px-5">
                                <!--begin::Chart-->
                                <div id="kt_project_overview_graph" class="card-rounded-bottom"
                                    style="height: 300px; min-height: 315px;">
                                    <div id="apexcharts225p5x0w"
                                        class="apexcharts-canvas apexcharts225p5x0w apexcharts-theme-light"
                                        style="width: 450px; height: 300px;"><svg id="SvgjsSvg1006" width="450"
                                            height="300" xmlns="http://www.w3.org/2000/svg" version="1.1"
                                            xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev"
                                            class="apexcharts-svg apexcharts-zoomable" xmlns:data="ApexChartsNS"
                                            transform="translate(0, 0)" style="background: transparent;">
                                            <foreignObject x="0" y="0" width="450" height="300">
                                                <div class="apexcharts-legend" xmlns="http://www.w3.org/1999/xhtml"
                                                    style="max-height: 150px;"></div>
                                            </foreignObject>
                                            <rect id="SvgjsRect1036" width="0" height="0" x="0" y="0" rx="0" ry="0"
                                                opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0"
                                                fill="#fefefe"></rect>
                                            <g id="SvgjsG1063" class="apexcharts-yaxis" rel="0"
                                                transform="translate(11.8984375, 0)">
                                                <g id="SvgjsG1064" class="apexcharts-yaxis-texts-g"><text
                                                        id="SvgjsText1066" font-family="Helvetica, Arial, sans-serif"
                                                        x="20" y="31.5" text-anchor="end" dominant-baseline="auto"
                                                        font-size="12px" font-weight="400" fill="#99a1b7"
                                                        class="apexcharts-text apexcharts-yaxis-label "
                                                        style="font-family: Helvetica, Arial, sans-serif;">
                                                        <tspan id="SvgjsTspan1067">80</tspan>
                                                        <title>80</title>
                                                    </text><text id="SvgjsText1069"
                                                        font-family="Helvetica, Arial, sans-serif" x="20" y="77.8078"
                                                        text-anchor="end" dominant-baseline="auto" font-size="12px"
                                                        font-weight="400" fill="#99a1b7"
                                                        class="apexcharts-text apexcharts-yaxis-label "
                                                        style="font-family: Helvetica, Arial, sans-serif;">
                                                        <tspan id="SvgjsTspan1070">75</tspan>
                                                        <title>75</title>
                                                    </text><text id="SvgjsText1072"
                                                        font-family="Helvetica, Arial, sans-serif" x="20" y="124.1156"
                                                        text-anchor="end" dominant-baseline="auto" font-size="12px"
                                                        font-weight="400" fill="#99a1b7"
                                                        class="apexcharts-text apexcharts-yaxis-label "
                                                        style="font-family: Helvetica, Arial, sans-serif;">
                                                        <tspan id="SvgjsTspan1073">70</tspan>
                                                        <title>70</title>
                                                    </text><text id="SvgjsText1075"
                                                        font-family="Helvetica, Arial, sans-serif" x="20"
                                                        y="170.42340000000002" text-anchor="end"
                                                        dominant-baseline="auto" font-size="12px" font-weight="400"
                                                        fill="#99a1b7" class="apexcharts-text apexcharts-yaxis-label "
                                                        style="font-family: Helvetica, Arial, sans-serif;">
                                                        <tspan id="SvgjsTspan1076">65</tspan>
                                                        <title>65</title>
                                                    </text><text id="SvgjsText1078"
                                                        font-family="Helvetica, Arial, sans-serif" x="20" y="216.7312"
                                                        text-anchor="end" dominant-baseline="auto" font-size="12px"
                                                        font-weight="400" fill="#99a1b7"
                                                        class="apexcharts-text apexcharts-yaxis-label "
                                                        style="font-family: Helvetica, Arial, sans-serif;">
                                                        <tspan id="SvgjsTspan1079">60</tspan>
                                                        <title>60</title>
                                                    </text><text id="SvgjsText1081"
                                                        font-family="Helvetica, Arial, sans-serif" x="20" y="263.039"
                                                        text-anchor="end" dominant-baseline="auto" font-size="12px"
                                                        font-weight="400" fill="#99a1b7"
                                                        class="apexcharts-text apexcharts-yaxis-label "
                                                        style="font-family: Helvetica, Arial, sans-serif;">
                                                        <tspan id="SvgjsTspan1082">55</tspan>
                                                        <title>55</title>
                                                    </text></g>
                                            </g>
                                            <g id="SvgjsG1008" class="apexcharts-inner apexcharts-graphical"
                                                transform="translate(41.8984375, 30)">
                                                <defs id="SvgjsDefs1007">
                                                    <clipPath id="gridRectMask225p5x0w">
                                                        <rect id="SvgjsRect1011" width="392.90234375" height="247.539"
                                                            x="-5" y="-8" rx="0" ry="0" opacity="1" stroke-width="0"
                                                            stroke="none" stroke-dasharray="0" fill="#fff"></rect>
                                                    </clipPath>
                                                    <clipPath id="forecastMask225p5x0w"></clipPath>
                                                    <clipPath id="nonForecastMask225p5x0w"></clipPath>
                                                    <clipPath id="gridRectMarkerMask225p5x0w">
                                                        <rect id="SvgjsRect1012" width="389.90234375" height="235.539"
                                                            x="-2" y="-2" rx="0" ry="0" opacity="1" stroke-width="0"
                                                            stroke="none" stroke-dasharray="0" fill="#fff"></rect>
                                                    </clipPath>
                                                </defs>
                                                <g id="SvgjsG1024" class="apexcharts-grid">
                                                    <g id="SvgjsG1025" class="apexcharts-gridlines-horizontal">
                                                        <line id="SvgjsLine1029" x1="0" y1="46.3078" x2="385.90234375"
                                                            y2="46.3078" stroke="#f1f1f4" stroke-dasharray="4"
                                                            stroke-linecap="butt" class="apexcharts-gridline"></line>
                                                        <line id="SvgjsLine1030" x1="0" y1="92.6156" x2="385.90234375"
                                                            y2="92.6156" stroke="#f1f1f4" stroke-dasharray="4"
                                                            stroke-linecap="butt" class="apexcharts-gridline"></line>
                                                        <line id="SvgjsLine1031" x1="0" y1="138.92340000000002"
                                                            x2="385.90234375" y2="138.92340000000002" stroke="#f1f1f4"
                                                            stroke-dasharray="4" stroke-linecap="butt"
                                                            class="apexcharts-gridline"></line>
                                                        <line id="SvgjsLine1032" x1="0" y1="185.2312" x2="385.90234375"
                                                            y2="185.2312" stroke="#f1f1f4" stroke-dasharray="4"
                                                            stroke-linecap="butt" class="apexcharts-gridline"></line>
                                                    </g>
                                                    <g id="SvgjsG1026" class="apexcharts-gridlines-vertical"></g>
                                                    <line id="SvgjsLine1035" x1="0" y1="231.539" x2="385.90234375"
                                                        y2="231.539" stroke="transparent" stroke-dasharray="0"
                                                        stroke-linecap="butt"></line>
                                                    <line id="SvgjsLine1034" x1="0" y1="1" x2="0" y2="231.539"
                                                        stroke="transparent" stroke-dasharray="0" stroke-linecap="butt">
                                                    </line>
                                                </g>
                                                <g id="SvgjsG1027" class="apexcharts-grid-borders">
                                                    <line id="SvgjsLine1028" x1="0" y1="0" x2="385.90234375" y2="0"
                                                        stroke="#f1f1f4" stroke-dasharray="4" stroke-linecap="butt"
                                                        class="apexcharts-gridline"></line>
                                                    <line id="SvgjsLine1033" x1="0" y1="231.539" x2="385.90234375"
                                                        y2="231.539" stroke="#f1f1f4" stroke-dasharray="4"
                                                        stroke-linecap="butt" class="apexcharts-gridline"></line>
                                                </g>
                                                <g id="SvgjsG1013"
                                                    class="apexcharts-area-series apexcharts-plot-series">
                                                    <g id="SvgjsG1014" class="apexcharts-series" zIndex="0"
                                                        seriesName="Incomplete" data:longestSeries="true" rel="1"
                                                        data:realIndex="0">
                                                        <path id="SvgjsPath1017"
                                                            d="M0 231.539L0 92.61559999999997C22.510970052083334 92.61559999999997 41.806087239583334 92.61559999999997 64.31705729166667 92.61559999999997C86.82802734375001 92.61559999999997 106.12314453125 0 128.63411458333334 0C151.14508463541668 0 170.44020182291666 0 192.951171875 0C215.46214192708334 0 234.75725911458335 46.30780000000004 257.2682291666667 46.30780000000004C279.77919921875 46.30780000000004 299.07431640625 46.30780000000004 321.5852864583333 46.30780000000004C344.09625651041665 46.30780000000004 363.39137369791666 46.30780000000004 385.90234375 46.30780000000004C385.90234375 46.30780000000004 385.90234375 46.30780000000004 385.90234375 231.539M385.90234375 46.30780000000004C385.90234375 46.30780000000004 385.90234375 46.30780000000004 385.90234375 46.30780000000004 "
                                                            fill="rgba(233,243,255,1)" fill-opacity="1"
                                                            stroke-opacity="1" stroke-linecap="butt" stroke-width="0"
                                                            stroke-dasharray="0" class="apexcharts-area" index="0"
                                                            clip-path="url(#gridRectMask225p5x0w)"
                                                            pathTo="M 0 231.539 L 0 92.61559999999997C 22.510970052083334 92.61559999999997 41.806087239583334 92.61559999999997 64.31705729166667 92.61559999999997C 86.82802734375001 92.61559999999997 106.12314453125 0 128.63411458333334 0C 151.14508463541668 0 170.44020182291666 0 192.951171875 0C 215.46214192708334 0 234.75725911458335 46.30780000000004 257.2682291666667 46.30780000000004C 279.77919921875 46.30780000000004 299.07431640625 46.30780000000004 321.5852864583333 46.30780000000004C 344.09625651041665 46.30780000000004 363.39137369791666 46.30780000000004 385.90234375 46.30780000000004C 385.90234375 46.30780000000004 385.90234375 46.30780000000004 385.90234375 231.539M 385.90234375 46.30780000000004z"
                                                            pathFrom="M -1 740.9248 L -1 740.9248 L 64.31705729166667 740.9248 L 128.63411458333334 740.9248 L 192.951171875 740.9248 L 257.2682291666667 740.9248 L 321.5852864583333 740.9248 L 385.90234375 740.9248">
                                                        </path>
                                                        <path id="SvgjsPath1018"
                                                            d="M0 92.61559999999997C22.510970052083334 92.61559999999997 41.806087239583334 92.61559999999997 64.31705729166667 92.61559999999997C86.82802734375001 92.61559999999997 106.12314453125 0 128.63411458333334 0C151.14508463541668 0 170.44020182291666 0 192.951171875 0C215.46214192708334 0 234.75725911458335 46.30780000000004 257.2682291666667 46.30780000000004C279.77919921875 46.30780000000004 299.07431640625 46.30780000000004 321.5852864583333 46.30780000000004C344.09625651041665 46.30780000000004 363.39137369791666 46.30780000000004 385.90234375 46.30780000000004C385.90234375 46.30780000000004 385.90234375 46.30780000000004 385.90234375 46.30780000000004 "
                                                            fill="none" fill-opacity="1" stroke="#1b84ff"
                                                            stroke-opacity="1" stroke-linecap="butt" stroke-width="3"
                                                            stroke-dasharray="0" class="apexcharts-area" index="0"
                                                            clip-path="url(#gridRectMask225p5x0w)"
                                                            pathTo="M 0 92.61559999999997C 22.510970052083334 92.61559999999997 41.806087239583334 92.61559999999997 64.31705729166667 92.61559999999997C 86.82802734375001 92.61559999999997 106.12314453125 0 128.63411458333334 0C 151.14508463541668 0 170.44020182291666 0 192.951171875 0C 215.46214192708334 0 234.75725911458335 46.30780000000004 257.2682291666667 46.30780000000004C 279.77919921875 46.30780000000004 299.07431640625 46.30780000000004 321.5852864583333 46.30780000000004C 344.09625651041665 46.30780000000004 363.39137369791666 46.30780000000004 385.90234375 46.30780000000004"
                                                            pathFrom="M -1 740.9248 L -1 740.9248 L 64.31705729166667 740.9248 L 128.63411458333334 740.9248 L 192.951171875 740.9248 L 257.2682291666667 740.9248 L 321.5852864583333 740.9248 L 385.90234375 740.9248"
                                                            fill-rule="evenodd"></path>
                                                        <g id="SvgjsG1015"
                                                            class="apexcharts-series-markers-wrap apexcharts-hidden-element-shown"
                                                            data:realIndex="0">
                                                            <g class="apexcharts-series-markers">
                                                                <circle id="SvgjsCircle1086" r="0" cx="0" cy="0"
                                                                    class="apexcharts-marker w1xh4jf0vg no-pointer-events"
                                                                    stroke="#1b84ff" fill="#e9f3ff" fill-opacity="1"
                                                                    stroke-width="3" stroke-opacity="0.9"
                                                                    default-marker-size="0"></circle>
                                                            </g>
                                                        </g>
                                                    </g>
                                                    <g id="SvgjsG1019" class="apexcharts-series" zIndex="1"
                                                        seriesName="Complete" data:longestSeries="true" rel="2"
                                                        data:realIndex="1">
                                                        <path id="SvgjsPath1022"
                                                            d="M0 231.539L0 231.53900000000004C22.510970052083334 231.53900000000004 41.806087239583334 231.53900000000004 64.31705729166667 231.53900000000004C86.82802734375001 231.53900000000004 106.12314453125 185.23120000000006 128.63411458333334 185.23120000000006C151.14508463541668 185.23120000000006 170.44020182291666 185.23120000000006 192.951171875 185.23120000000006C215.46214192708334 185.23120000000006 234.75725911458335 231.53900000000004 257.2682291666667 231.53900000000004C279.77919921875 231.53900000000004 299.07431640625 231.53900000000004 321.5852864583333 231.53900000000004C344.09625651041665 231.53900000000004 363.39137369791666 185.23120000000006 385.90234375 185.23120000000006C385.90234375 185.23120000000006 385.90234375 185.23120000000006 385.90234375 231.539M385.90234375 185.23120000000006C385.90234375 185.23120000000006 385.90234375 185.23120000000006 385.90234375 185.23120000000006 "
                                                            fill="rgba(223,255,234,1)" fill-opacity="1"
                                                            stroke-opacity="1" stroke-linecap="butt" stroke-width="0"
                                                            stroke-dasharray="0" class="apexcharts-area" index="1"
                                                            clip-path="url(#gridRectMask225p5x0w)"
                                                            pathTo="M 0 231.539 L 0 231.53900000000004C 22.510970052083334 231.53900000000004 41.806087239583334 231.53900000000004 64.31705729166667 231.53900000000004C 86.82802734375001 231.53900000000004 106.12314453125 185.23120000000006 128.63411458333334 185.23120000000006C 151.14508463541668 185.23120000000006 170.44020182291666 185.23120000000006 192.951171875 185.23120000000006C 215.46214192708334 185.23120000000006 234.75725911458335 231.53900000000004 257.2682291666667 231.53900000000004C 279.77919921875 231.53900000000004 299.07431640625 231.53900000000004 321.5852864583333 231.53900000000004C 344.09625651041665 231.53900000000004 363.39137369791666 185.23120000000006 385.90234375 185.23120000000006C 385.90234375 185.23120000000006 385.90234375 185.23120000000006 385.90234375 231.539M 385.90234375 185.23120000000006z"
                                                            pathFrom="M -1 740.9248 L -1 740.9248 L 64.31705729166667 740.9248 L 128.63411458333334 740.9248 L 192.951171875 740.9248 L 257.2682291666667 740.9248 L 321.5852864583333 740.9248 L 385.90234375 740.9248">
                                                        </path>
                                                        <path id="SvgjsPath1023"
                                                            d="M0 231.53900000000004C22.510970052083334 231.53900000000004 41.806087239583334 231.53900000000004 64.31705729166667 231.53900000000004C86.82802734375001 231.53900000000004 106.12314453125 185.23120000000006 128.63411458333334 185.23120000000006C151.14508463541668 185.23120000000006 170.44020182291666 185.23120000000006 192.951171875 185.23120000000006C215.46214192708334 185.23120000000006 234.75725911458335 231.53900000000004 257.2682291666667 231.53900000000004C279.77919921875 231.53900000000004 299.07431640625 231.53900000000004 321.5852864583333 231.53900000000004C344.09625651041665 231.53900000000004 363.39137369791666 185.23120000000006 385.90234375 185.23120000000006C385.90234375 185.23120000000006 385.90234375 185.23120000000006 385.90234375 185.23120000000006 "
                                                            fill="none" fill-opacity="1" stroke="#17c653"
                                                            stroke-opacity="1" stroke-linecap="butt" stroke-width="3"
                                                            stroke-dasharray="0" class="apexcharts-area" index="1"
                                                            clip-path="url(#gridRectMask225p5x0w)"
                                                            pathTo="M 0 231.53900000000004C 22.510970052083334 231.53900000000004 41.806087239583334 231.53900000000004 64.31705729166667 231.53900000000004C 86.82802734375001 231.53900000000004 106.12314453125 185.23120000000006 128.63411458333334 185.23120000000006C 151.14508463541668 185.23120000000006 170.44020182291666 185.23120000000006 192.951171875 185.23120000000006C 215.46214192708334 185.23120000000006 234.75725911458335 231.53900000000004 257.2682291666667 231.53900000000004C 279.77919921875 231.53900000000004 299.07431640625 231.53900000000004 321.5852864583333 231.53900000000004C 344.09625651041665 231.53900000000004 363.39137369791666 185.23120000000006 385.90234375 185.23120000000006"
                                                            pathFrom="M -1 740.9248 L -1 740.9248 L 64.31705729166667 740.9248 L 128.63411458333334 740.9248 L 192.951171875 740.9248 L 257.2682291666667 740.9248 L 321.5852864583333 740.9248 L 385.90234375 740.9248"
                                                            fill-rule="evenodd"></path>
                                                        <g id="SvgjsG1020"
                                                            class="apexcharts-series-markers-wrap apexcharts-hidden-element-shown"
                                                            data:realIndex="1">
                                                            <g class="apexcharts-series-markers">
                                                                <circle id="SvgjsCircle1087" r="0" cx="0" cy="0"
                                                                    class="apexcharts-marker wbpff1nb9 no-pointer-events"
                                                                    stroke="#17c653" fill="#dfffea" fill-opacity="1"
                                                                    stroke-width="3" stroke-opacity="0.9"
                                                                    default-marker-size="0"></circle>
                                                            </g>
                                                        </g>
                                                    </g>
                                                    <g id="SvgjsG1016" class="apexcharts-datalabels" data:realIndex="0">
                                                    </g>
                                                    <g id="SvgjsG1021" class="apexcharts-datalabels" data:realIndex="1">
                                                    </g>
                                                </g>
                                                <line id="SvgjsLine1037" x1="0" y1="0" x2="0" y2="231.539"
                                                    stroke="#1b84ff" stroke-dasharray="3" stroke-linecap="butt"
                                                    class="apexcharts-xcrosshairs" x="0" y="0" width="1"
                                                    height="231.539" fill="#b1b9c4" filter="none" fill-opacity="0.9"
                                                    stroke-width="1"></line>
                                                <line id="SvgjsLine1038" x1="0" y1="0" x2="385.90234375" y2="0"
                                                    stroke="#b6b6b6" stroke-dasharray="0" stroke-width="1"
                                                    stroke-linecap="butt" class="apexcharts-ycrosshairs"></line>
                                                <line id="SvgjsLine1039" x1="0" y1="0" x2="385.90234375" y2="0"
                                                    stroke-dasharray="0" stroke-width="0" stroke-linecap="butt"
                                                    class="apexcharts-ycrosshairs-hidden"></line>
                                                <g id="SvgjsG1040" class="apexcharts-xaxis" transform="translate(0, 0)">
                                                    <g id="SvgjsG1041" class="apexcharts-xaxis-texts-g"
                                                        transform="translate(0, -4)"><text id="SvgjsText1043"
                                                            font-family="Helvetica, Arial, sans-serif" x="0" y="260.539"
                                                            text-anchor="middle" dominant-baseline="auto"
                                                            font-size="12px" font-weight="400" fill="#99a1b7"
                                                            class="apexcharts-text apexcharts-xaxis-label "
                                                            style="font-family: Helvetica, Arial, sans-serif;">
                                                            <tspan id="SvgjsTspan1044">Feb</tspan>
                                                            <title>Feb</title>
                                                        </text><text id="SvgjsText1046"
                                                            font-family="Helvetica, Arial, sans-serif"
                                                            x="64.31705729166666" y="260.539" text-anchor="middle"
                                                            dominant-baseline="auto" font-size="12px" font-weight="400"
                                                            fill="#99a1b7"
                                                            class="apexcharts-text apexcharts-xaxis-label "
                                                            style="font-family: Helvetica, Arial, sans-serif;">
                                                            <tspan id="SvgjsTspan1047">Mar</tspan>
                                                            <title>Mar</title>
                                                        </text><text id="SvgjsText1049"
                                                            font-family="Helvetica, Arial, sans-serif"
                                                            x="128.63411458333334" y="260.539" text-anchor="middle"
                                                            dominant-baseline="auto" font-size="12px" font-weight="400"
                                                            fill="#99a1b7"
                                                            class="apexcharts-text apexcharts-xaxis-label "
                                                            style="font-family: Helvetica, Arial, sans-serif;">
                                                            <tspan id="SvgjsTspan1050">Apr</tspan>
                                                            <title>Apr</title>
                                                        </text><text id="SvgjsText1052"
                                                            font-family="Helvetica, Arial, sans-serif"
                                                            x="192.95117187500003" y="260.539" text-anchor="middle"
                                                            dominant-baseline="auto" font-size="12px" font-weight="400"
                                                            fill="#99a1b7"
                                                            class="apexcharts-text apexcharts-xaxis-label "
                                                            style="font-family: Helvetica, Arial, sans-serif;">
                                                            <tspan id="SvgjsTspan1053">May</tspan>
                                                            <title>May</title>
                                                        </text><text id="SvgjsText1055"
                                                            font-family="Helvetica, Arial, sans-serif"
                                                            x="257.26822916666674" y="260.539" text-anchor="middle"
                                                            dominant-baseline="auto" font-size="12px" font-weight="400"
                                                            fill="#99a1b7"
                                                            class="apexcharts-text apexcharts-xaxis-label "
                                                            style="font-family: Helvetica, Arial, sans-serif;">
                                                            <tspan id="SvgjsTspan1056">Jun</tspan>
                                                            <title>Jun</title>
                                                        </text><text id="SvgjsText1058"
                                                            font-family="Helvetica, Arial, sans-serif"
                                                            x="321.5852864583334" y="260.539" text-anchor="middle"
                                                            dominant-baseline="auto" font-size="12px" font-weight="400"
                                                            fill="#99a1b7"
                                                            class="apexcharts-text apexcharts-xaxis-label "
                                                            style="font-family: Helvetica, Arial, sans-serif;">
                                                            <tspan id="SvgjsTspan1059">Jul</tspan>
                                                            <title>Jul</title>
                                                        </text><text id="SvgjsText1061"
                                                            font-family="Helvetica, Arial, sans-serif"
                                                            x="385.9023437500001" y="260.539" text-anchor="middle"
                                                            dominant-baseline="auto" font-size="12px" font-weight="400"
                                                            fill="#99a1b7"
                                                            class="apexcharts-text apexcharts-xaxis-label "
                                                            style="font-family: Helvetica, Arial, sans-serif;">
                                                            <tspan id="SvgjsTspan1062">Aug</tspan>
                                                            <title>Aug</title>
                                                        </text></g>
                                                </g>
                                                <g id="SvgjsG1083"
                                                    class="apexcharts-yaxis-annotations apexcharts-hidden-element-shown">
                                                </g>
                                                <g id="SvgjsG1084"
                                                    class="apexcharts-xaxis-annotations apexcharts-hidden-element-shown">
                                                </g>
                                                <g id="SvgjsG1085"
                                                    class="apexcharts-point-annotations apexcharts-hidden-element-shown">
                                                </g>
                                                <rect id="SvgjsRect1088" width="0" height="0" x="0" y="0" rx="0" ry="0"
                                                    opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0"
                                                    fill="#fefefe" class="apexcharts-zoom-rect"></rect>
                                                <rect id="SvgjsRect1089" width="0" height="0" x="0" y="0" rx="0" ry="0"
                                                    opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0"
                                                    fill="#fefefe" class="apexcharts-selection-rect"></rect>
                                            </g>
                                        </svg>
                                        <div class="apexcharts-tooltip apexcharts-theme-light">
                                            <div class="apexcharts-tooltip-title"
                                                style="font-family: Helvetica, Arial, sans-serif; font-size: 12px;">
                                            </div>
                                            <div class="apexcharts-tooltip-series-group" style="order: 1;"><span
                                                    class="apexcharts-tooltip-marker"
                                                    style="background-color: rgb(233, 243, 255);"></span>
                                                <div class="apexcharts-tooltip-text"
                                                    style="font-family: Helvetica, Arial, sans-serif; font-size: 12px;">
                                                    <div class="apexcharts-tooltip-y-group"><span
                                                            class="apexcharts-tooltip-text-y-label"></span><span
                                                            class="apexcharts-tooltip-text-y-value"></span></div>
                                                    <div class="apexcharts-tooltip-goals-group"><span
                                                            class="apexcharts-tooltip-text-goals-label"></span><span
                                                            class="apexcharts-tooltip-text-goals-value"></span></div>
                                                    <div class="apexcharts-tooltip-z-group"><span
                                                            class="apexcharts-tooltip-text-z-label"></span><span
                                                            class="apexcharts-tooltip-text-z-value"></span></div>
                                                </div>
                                            </div>
                                            <div class="apexcharts-tooltip-series-group" style="order: 2;"><span
                                                    class="apexcharts-tooltip-marker"
                                                    style="background-color: rgb(223, 255, 234);"></span>
                                                <div class="apexcharts-tooltip-text"
                                                    style="font-family: Helvetica, Arial, sans-serif; font-size: 12px;">
                                                    <div class="apexcharts-tooltip-y-group"><span
                                                            class="apexcharts-tooltip-text-y-label"></span><span
                                                            class="apexcharts-tooltip-text-y-value"></span></div>
                                                    <div class="apexcharts-tooltip-goals-group"><span
                                                            class="apexcharts-tooltip-text-goals-label"></span><span
                                                            class="apexcharts-tooltip-text-goals-value"></span></div>
                                                    <div class="apexcharts-tooltip-z-group"><span
                                                            class="apexcharts-tooltip-text-z-label"></span><span
                                                            class="apexcharts-tooltip-text-z-value"></span></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="apexcharts-xaxistooltip apexcharts-xaxistooltip-bottom apexcharts-theme-light">
                                            <div class="apexcharts-xaxistooltip-text"
                                                style="font-family: Helvetica, Arial, sans-serif; font-size: 12px;">
                                            </div>
                                        </div>
                                        <div
                                            class="apexcharts-yaxistooltip apexcharts-yaxistooltip-0 apexcharts-yaxistooltip-left apexcharts-theme-light">
                                            <div class="apexcharts-yaxistooltip-text"></div>
                                        </div>
                                    </div>
                                </div>
                                <!--end::Chart-->
                            </div>
                            <!--end::Card body-->
                        </div>
                        <!--end::Graph-->
                    </div>
                    <!--end::Col-->
                </div>
                <!--end::Row-->
            </div>
            <!--end::Content container-->
        </div>
        <!--end::Content-->
    </div>
    <!--end::Content wrapper-->
    <!--begin::Footer-->
    {{> surveyDetailsFooter}}
    <!--end::Footer-->
</div>

{{> surveyDetailsModals}}

{{#block 'scripts'}}

{{/block}}